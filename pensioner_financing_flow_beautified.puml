@startuml Pensioner Financing Transaction Flow - Double Credit Issue

' Theme and Global Styling Configuration
!theme aws-orange                        ' Apply AWS Orange theme for professional appearance
skinparam backgroundColor #f8f9fa         ' Set light gray background color for the entire diagram
skinparam handwritten false              ' Disable handwritten style for clean, professional look
skinparam shadowing false                ' Disable shadows for cleaner appearance
skinparam roundcorner 10                 ' Set rounded corners (10px radius) for modern look
skinparam sequenceMessageAlign center    ' Center-align sequence message text

' Participant (Actor) Styling Configuration
skinparam participant {
    BackgroundColor #e3f2fd         ' Light blue background for participant boxes
    BorderColor #1976d2             ' Dark blue border color for participant boxes
    FontColor #0d47a1               ' Dark blue font color for participant names
    FontSize 12                     ' Font size for participant names
    FontStyle bold                  ' Bold font style for participant names
}

' Note/Annotation Styling Configuration
skinparam note {
    BackgroundColor #fff3e0         ' Light orange background for note boxes
    BorderColor #f57c00             ' Orange border color for note boxes
    FontColor #e65100               ' Dark orange font color for note text
    FontSize 10                     ' Smaller font size for note content
}

' Arrow/Message Styling Configuration
skinparam arrow {
    Color #1976d2                   ' Dark blue color for arrows/message lines
    FontColor #0d47a1               ' Dark blue font color for message text
    FontSize 10                     ' Font size for message labels
}

title <size:16><color:#d32f2f><b>🏦 Auto Debit/Auto Grab Transactions for Pensioner Financing</b></color></size>\n<size:14><color:#f57c00><b>⚠️ Double Credit Issue Analysis</b></color></size>

participant "🏛️ **SIBS**\n<size:10><i>Banking System</i></size>" as SIBS #lightblue
participant "🌐 **NGINX**\n<size:10><i>Load Balancer</i></size>" as NGINX #lightgreen
participant "⚙️ **CL**\n<size:10><i>Core Logic</i></size>" as CL #lightyellow
participant "💰 **MOBIUS**\n<size:10><i>Payment Engine</i></size>" as MOBIUS #lightcoral

== 🔄 **Initial Transaction Flow (R1)** ==

SIBS -> NGINX: 📤 **R1** - Auto Debit/Auto Grab Transaction Request\n<color:#666><size:9>Transaction ID: R1</size></color>
activate NGINX
NGINX -> CL: 📨 Forward R1 message
activate CL
CL -> MOBIUS: 📨 Forward R1 message
activate MOBIUS

note right of MOBIUS #e8f5e8
<color:#2e7d32><b>✅ Processing R1</b></color>
• Transaction validated
• Amount credited to Pensioner
• Status: SUCCESS
end note

MOBIUS -> MOBIUS: 🔄 Process R1 successfully\n💳 Credit amount to Pensioner
MOBIUS -> CL: 📤 **RS1** response message
deactivate MOBIUS
CL -> CL: ✅ Validate RS1 message\n<color:#4caf50>(Valid format confirmed)</color>
CL -> NGINX: 📨 Forward RS1 message
deactivate CL
NGINX -> SIBS: 📨 Forward RS1 message
deactivate NGINX

== ❌ **Validation Issue at SIBS** ==

note over SIBS #ffebee
<color:#c62828><b>🚫 RS1 Validation Failed</b></color>
Expected formats:
• F1 (success crediting)
• 41 (failed)
• 42 (rejected)

<color:#d32f2f><b>Result: VALIDATION FAILED</b></color>
end note

SIBS -> SIBS: 🔍 Validate RS1 message\n<color:#d32f2f>❌ VALIDATION FAILED</color>

== 🔁 **Automatic Retry Flow (R2)** ==

SIBS -> NGINX: 📤 **R2** - Auto Debit/Auto Grab Transaction Request\n<color:#ff9800><size:9>Transaction ID: R2 (Auto Retry)</size></color>
activate NGINX
NGINX -> CL: 📨 Forward R2 message
activate CL
CL -> MOBIUS: 📨 Forward R2 message
activate MOBIUS

note right of MOBIUS #fff3e0
<color:#f57c00><b>⚠️ Processing R2</b></color>
• Same transaction as R1
• Amount credited AGAIN
• Status: SUCCESS
<color:#d32f2f><b>⚠️ DUPLICATE PROCESSING!</b></color>
end note

MOBIUS -> MOBIUS: 🔄 Process R2 successfully\n💳 Credit amount to Pensioner **AGAIN**
MOBIUS -> CL: 📤 **RS2** response message
deactivate MOBIUS
CL -> CL: ✅ Validate RS2 message\n<color:#4caf50>(Valid format confirmed)</color>
CL -> NGINX: 📨 Forward RS2 message
deactivate CL
NGINX -> SIBS: 📨 Forward RS2 message
deactivate NGINX

== ✅ **Successful Validation** ==

note over SIBS #e8f5e8
<color:#2e7d32><b>✅ RS2 Validation Success</b></color>
Format: F1 (success crediting)
<color:#4caf50><b>Result: VALIDATION PASSED</b></color>
end note

SIBS -> SIBS: 🔍 Validate RS2 message\n<color:#4caf50>✅ SUCCESS (F1 format)</color>

== 🔍 **Manual Verification & Issue Discovery** ==

note over SIBS, MOBIUS #f3e5f5
<color:#7b1fa2><b>🔍 Manual Verification Process</b></color>

**Findings:**
• R1 and R2 are identical requests
• Both transactions were successfully processed
• Double crediting occurred to Pensioner account
• Financial impact: 2x intended amount credited
end note

note right of MOBIUS #ffcdd2
<color:#c62828><b>🚨 CRITICAL ISSUE IDENTIFIED</b></color>

<color:#d32f2f><b>💸 Double Credit to Pensioner</b></color>
• **R1 transaction:** ✅ SUCCESS
• **R2 transaction:** ✅ SUCCESS (duplicate)
• **Root cause:** RS1 validation failure at SIBS
• **Impact:** Financial loss due to duplicate processing

<color:#1976d2><b>🔧 Recommended Actions:</b></color>
• Implement idempotency checks
• Enhance response format validation
• Add duplicate transaction detection
end note

@enduml
