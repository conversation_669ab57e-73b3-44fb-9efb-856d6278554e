Please refer to the steps below:

1.	<PERSON><PERSON><PERSON> initiated the transaction request with the new transaction request id R1 - “Auto Debit / Auto Grab Transactions for Pensioner Financing” that is meant for MOBIUS
2.	NGINX received R1 message from SIBS and sent to CL
3.	<PERSON><PERSON> received the R1 message from NGINX and sent to MOBIUS
4.	MOBIUS received R1 message from CL
5.	MOBIUS processed the R1 message successfully and credited the amount to Pensioner
6.	After crediting the amount, MOBIUS generated the response RS1
7.	MOBIUS responded to CL with RS1 message for the R1 request
8.	<PERSON><PERSON> received the RS1 message from MOBIUS for the R1 request
9.	<PERSON><PERSON> validated the RS1 message and it is in the valid response format without any missing information
10.	<PERSON><PERSON> sent the RS1 message to NGINX that is meant for SIBS
11.	NGINX received RS1 message from CL and sent to SIBS
12.	SIBS received the RS1 message from NGINX
13.	SIBS validated the RS1 message to ensure it is in F1 (success crediting) or 41(failed) or 42 (rejected) response format
14.	SIBS validation for RS1 message has failed as it is not in the response format of F1 or 41 or 42
15.	As RS1 message validation has failed SIBS has automatically retriggered the R1 transaction request with the new transaction request id R2 - “Auto Debit / Auto Grab Transactions for Pensioner Financing” that is meant for MOBIUS 
16.	NGINX received R2 message from SIBS and sent to CL
17.	CL received the R2 message from NGINX and sent to MOBIUS
18.	MOBIUS received R2 message from CL
19.	MOBIUS processed the R2 message successfully and credited the amount to Pensioner
20.	After crediting the amount, MOBIUS generated the response RS2
21.	MOBIUS responded to CL with RS2 message for the R2 request
22.	CL received the RS2 message from MOBIUS for the R2 request
23.	CL validated the RS2 message and it is in the valid response format without any missing information
24.	CL sent the RS2 message to NGINX that is meant for SIBS
25.	NGINX received RS2 message from CL and sent to SIBS
26.	SIBS received the RS2 message from NGINX
27.	SIBS validated the RS2 message to ensure it is in F1 (success crediting) or 41(failed) or 42 (rejected) response format
28.	SIBS validation for RS2 message has been successful and it is in the response format of F1
29.	Manual verification of R1, R2 transactions request initiated
30.	During the manual verification process, it was identified both R1 and R2 are the same requests
31.	Identified both R1 and R2 transactions were successfully executed by MOBIUS to credit the amount to Pensioner and has caused double crediting issue
