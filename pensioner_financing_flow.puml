@startuml Pensioner Financing Transaction Flow - Double Credit Issue

title Auto Debit/Auto Grab Transactions for Pensioner Financing\nDouble Credit Issue Flow

participant SIBS
participant NGINX
participant CL
participant MOBIUS

note over SIBS, MOBIUS: Initial Transaction Flow (R1)

SIBS -> NGINX: R1 - Auto Debit/Auto Grab Transaction Request\n(Transaction ID: R1)
NGINX -> CL: Forward R1 message
CL -> MOBIUS: Forward R1 message
MOBIUS -> MOBIUS: Process R1 successfully\nCredit amount to Pensioner
MOBIUS -> CL: RS1 response message
CL -> CL: Validate RS1 message\n(Valid format confirmed)
CL -> NGINX: Forward RS1 message
NGINX -> SIBS: Forward RS1 message

note over SIBS: Validation Issue
SIBS -> SIBS: Validate RS1 message\nExpected: F1 (success) or 41 (failed) or 42 (rejected)\nResult: VALIDATION FAILED

note over SIBS, MOBIUS: Automatic Retry Flow (R2)

SIBS -> NGINX: R2 - Auto Debit/Auto Grab Transaction Request\n(Transaction ID: R2 - Auto Retry)
NGINX -> CL: Forward R2 message
CL -> MOBIUS: Forward R2 message
MOBIUS -> MOBIUS: Process R2 successfully\nCredit amount to Pensioner AGAIN
MOBIUS -> CL: RS2 response message
CL -> CL: Validate RS2 message\n(Valid format confirmed)
CL -> NGINX: Forward RS2 message
NGINX -> SIBS: Forward RS2 message

note over SIBS: Successful Validation
SIBS -> SIBS: Validate RS2 message\nResult: SUCCESS (F1 format)

note over SIBS, MOBIUS: Manual Verification Process

note over SIBS, MOBIUS
Manual verification initiated
Discovered:
- R1 and R2 are identical requests
- Both transactions were successfully processed
- Double crediting occurred to Pensioner account
end note

note right of MOBIUS #ffcccc
ISSUE IDENTIFIED:
Double Credit to Pensioner
- R1 transaction: SUCCESS
- R2 transaction: SUCCESS (duplicate)
- Root cause: RS1 validation failure at SIBS
end note

@enduml
